{% extends 'estoque/base.html' %}

{% block title %}Grupo de Previsões - Controle de Estoque{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1>Grupo de Previsões</h1>
        <div>
            <a href="{% url 'previsao-demanda-list' %}" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> Voltar
            </a>
            <a href="{% url 'previsao-demanda-grupo-delete' grupo_sessao %}" class="btn btn-danger" 
               onclick="return confirm('Tem certeza que deseja excluir este grupo de previsões?')">
                <i class="fas fa-trash"></i> Excluir Grupo
            </a>
        </div>
    </div>

    <div class="row mb-4">
        <div class="col-md-12">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold">Informações do Grupo</h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-4">
                            <strong>ID do Grupo:</strong> {{ grupo_sessao }}
                        </div>
                        <div class="col-md-4">
                            <strong>Data de Criação:</strong> {{ data_criacao|date:"d/m/Y H:i" }}
                        </div>
                        <div class="col-md-4">
                            <strong>Total de Previsões:</strong> {{ previsoes|length }}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="card shadow">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold">Previsões do Grupo</h6>
        </div>
        <div class="card-body">
            {% if previsoes %}
                <div class="table-responsive">
                    <table class="table table-bordered table-striped">
                        <thead>
                            <tr>
                                <th>Mola</th>
                                <th>Cliente</th>
                                <th>Período</th>
                                <th>Método</th>
                                <th>Quantidade Prevista</th>
                                <th>Validade</th>
                                {% if previsoes.0.precisao %}
                                <th>Precisão</th>
                                {% endif %}
                                <th>Ações</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for previsao in previsoes %}
                                <tr>
                                    <td>{{ previsao.mola.codigo }}</td>
                                    <td>{{ previsao.mola.cliente }}</td>
                                    <td>{{ previsao.get_periodo_display }}</td>
                                    <td>{{ previsao.get_metodo_display }}</td>
                                    <td>{{ previsao.quantidade_prevista }}</td>
                                    <td>{{ previsao.data_inicio|date:"d/m/Y" }} a {{ previsao.data_fim|date:"d/m/Y" }}</td>
                                    {% if previsao.precisao %}
                                    <td>{{ previsao.precisao|floatformat:1 }}%</td>
                                    {% endif %}
                                    <td>
                                        <a href="{% url 'previsao-demanda-detail' previsao.id %}" class="btn btn-sm btn-info">
                                            <i class="fas fa-eye"></i> Detalhes
                                        </a>
                                    </td>
                                </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>

                <!-- Resumo estatístico -->
                <div class="row mt-4">
                    <div class="col-md-12">
                        <div class="card">
                            <div class="card-header">
                                <h6 class="m-0 font-weight-bold">Resumo Estatístico</h6>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-3">
                                        <strong>Total de Molas:</strong> {{ previsoes|length }}
                                    </div>
                                    <div class="col-md-3">
                                        <strong>Quantidade Total Prevista:</strong> 
                                        {% with total=0 %}
                                            {% for previsao in previsoes %}
                                                {% with total=total|add:previsao.quantidade_prevista %}{% endwith %}
                                            {% endfor %}
                                            {{ total }}
                                        {% endwith %}
                                    </div>
                                    {% if previsoes.0.precisao %}
                                    <div class="col-md-3">
                                        <strong>Precisão Média:</strong>
                                        {% with total_precisao=0 count=0 %}
                                            {% for previsao in previsoes %}
                                                {% if previsao.precisao %}
                                                    {% with total_precisao=total_precisao|add:previsao.precisao count=count|add:1 %}{% endwith %}
                                                {% endif %}
                                            {% endfor %}
                                            {% if count > 0 %}
                                                {{ total_precisao|div:count|floatformat:1 }}%
                                            {% else %}
                                                N/A
                                            {% endif %}
                                        {% endwith %}
                                    </div>
                                    {% endif %}
                                    <div class="col-md-3">
                                        <strong>Período:</strong> {{ previsoes.0.get_periodo_display }}
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            {% else %}
                <div class="alert alert-info">
                    <i class="fas fa-info-circle"></i>
                    Nenhuma previsão encontrada neste grupo.
                </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}
