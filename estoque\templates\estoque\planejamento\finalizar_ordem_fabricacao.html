{% extends 'estoque/base.html' %}

{% block title %}Finalizar Ordem de Fabricação - Molas Rios{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="mt-4">Finalizar Ordem de Fabricação</h1>
        <div>
            <a href="{% url 'planejamento-detail' planejamento.id %}" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> Voltar
            </a>
        </div>
    </div>

    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">Detalhes da Ordem de Fabricação</h6>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-6">
                    <p><strong>Ordem:</strong> {{ planejamento.nome }}</p>
                    <p><strong>Data de Emissão:</strong>
                        {% if planejamento.status == 'P' %}
                            -
                        {% else %}
                            {{ planejamento.data_inicio|date:"d/m/Y" }}
                        {% endif %}
                    </p>
                    <p><strong>Status:</strong>
                        {% if planejamento.status == 'P' %}
                            <span class="badge bg-primary">Pendente</span>
                        {% elif planejamento.status == 'E' %}
                            <span class="badge bg-info">Em Produção</span>
                        {% elif planejamento.status == 'C' %}
                            <span class="badge bg-success">Concluído</span>
                        {% elif planejamento.status == 'A' %}
                            <span class="badge bg-warning">Atrasado</span>
                        {% elif planejamento.status == 'X' %}
                            <span class="badge bg-danger">Cancelado</span>
                        {% endif %}
                    </p>
                </div>
                <div class="col-md-6">
                    <p><strong>Mola:</strong> {{ item.mola.codigo }}</p>
                    <p><strong>Material:</strong>
                        {% if planejamento.material %}
                            {{ planejamento.material.nome }} - {{ planejamento.material.diametro }}
                        {% elif item.mola.material %}
                            {{ item.mola.material.nome }} - {{ item.mola.material.diametro }}
                        {% else %}
                            Não especificado
                        {% endif %}
                    </p>
                    <p><strong>Quantidade a Produzir:</strong> {{ item.quantidade }}</p>
                </div>
            </div>
        </div>
    </div>

    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">Finalizar Produção</h6>
        </div>
        <div class="card-body">
            <div class="alert alert-info">
                <i class="fas fa-info-circle"></i> Informe a quantidade de molas produzidas que serão destinadas para venda direta e para estoque.
            </div>

            <form method="post">
                {% csrf_token %}

                {% if form.non_field_errors %}
                    <div class="alert alert-danger">
                        {% for error in form.non_field_errors %}
                            {{ error }}
                        {% endfor %}
                    </div>
                {% endif %}

                <div class="row mb-3">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="{{ form.quantidade_produzida.id_for_label }}" class="form-label">{{ form.quantidade_produzida.label }}</label>
                            {{ form.quantidade_produzida }}
                            {% if form.quantidade_produzida.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.quantidade_produzida.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                            <div class="form-text">Quantidade total produzida que será adicionada ao estoque</div>
                        </div>
                    </div>
                </div>

                <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                    <button type="submit" class="btn btn-success">
                        <i class="fas fa-check-circle"></i> Finalizar Ordem
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        const quantidadeVendida = document.getElementById('id_quantidade_vendida');
        const quantidadeEstoque = document.getElementById('id_quantidade_estoque');
        const quantidadeTotal = {{ item.quantidade }};

        // Função para atualizar o outro campo
        function atualizarCampos() {
            const vendida = parseInt(quantidadeVendida.value) || 0;
            const estoque = parseInt(quantidadeEstoque.value) || 0;
            const total = vendida + estoque;

            // Adicionar aviso se o total for diferente da quantidade planejada
            const form = quantidadeVendida.closest('form');
            let avisoEl = form.querySelector('.aviso-quantidade');

            if (!avisoEl) {
                avisoEl = document.createElement('div');
                avisoEl.className = 'alert mt-3 aviso-quantidade';
                form.insertBefore(avisoEl, form.querySelector('.d-grid'));
            }

            if (total > quantidadeTotal) {
                avisoEl.className = 'alert alert-warning mt-3 aviso-quantidade';
                avisoEl.innerHTML = `<i class="fas fa-exclamation-triangle"></i> Atenção: O total (${total}) é maior que a quantidade planejada (${quantidadeTotal}).`;
            } else if (total < quantidadeTotal) {
                avisoEl.className = 'alert alert-info mt-3 aviso-quantidade';
                avisoEl.innerHTML = `<i class="fas fa-info-circle"></i> O total (${total}) é menor que a quantidade planejada (${quantidadeTotal}).`;
            } else {
                avisoEl.className = 'alert alert-success mt-3 aviso-quantidade';
                avisoEl.innerHTML = `<i class="fas fa-check-circle"></i> O total (${total}) é igual à quantidade planejada (${quantidadeTotal}).`;
            }
        }

        // Adicionar event listeners
        quantidadeVendida.addEventListener('input', atualizarCampos);
        quantidadeEstoque.addEventListener('input', atualizarCampos);

        // Inicializar
        atualizarCampos();
    });
</script>
{% endblock %}
