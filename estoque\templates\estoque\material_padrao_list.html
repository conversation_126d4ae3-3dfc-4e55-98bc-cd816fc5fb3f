{% extends 'estoque/base.html' %}

{% block title %}Materiais Padrão - Molas Rios{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1>Materia<PERSON></h1>
        <div>
            <a href="{% url 'materiais-padrao-pdf' %}" class="btn btn-success" title="Exportar PDF">
                <i class="fas fa-file-pdf"></i> Exportar PDF
            </a>
            <a href="{% url 'material-padrao-create' %}" class="btn btn-primary">
                <i class="fas fa-plus"></i> Novo Material Padrão
            </a>
            <a href="{% url 'material-list' %}" class="btn btn-info">
                <i class="fas fa-list"></i> Listar Materiais
            </a>
        </div>
    </div>

    <div class="card shadow">
        <div class="card-body">
            {% if materiais_padrao %}
                <div class="table-responsive">
                    <table class="table table-bordered table-striped">
                        <thead>
                            <tr>
                                <th>Nome</th>
                                <th>Diâmetro</th>
                                <th>Estoque (kg)</th>
                                <th>Estoque Mínimo</th>
                                <th>Status</th>
                                <th>Ações</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for material_padrao in materiais_padrao %}
                                <tr>
                                    <td>{{ material_padrao.nome }}</td>
                                    <td>{{ material_padrao.diametro }}</td>
                                    <td>{{ material_padrao.estoque_total|floatformat:2 }}</td>
                                    <td>{{ material_padrao.estoque_minimo_total|floatformat:2 }}</td>
                                    <td>
                                        {% if material_padrao.estoque_total == 0 %}
                                            <span class="badge bg-secondary">N/A</span>
                                        {% elif material_padrao.estoque_baixo %}
                                            <span class="badge bg-danger">Estoque Baixo</span>
                                        {% else %}
                                            <span class="badge bg-success">OK</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <a href="{% url 'material-padrao-detail' material_padrao.id %}" class="btn btn-sm btn-info" data-bs-toggle="tooltip" data-bs-placement="top" title="Informações do Material Padrão">
                                            <i class="fas fa-info-circle"></i>
                                        </a>
                                        <a href="{% url 'material-padrao-update' material_padrao.id %}" class="btn btn-sm btn-warning" data-bs-toggle="tooltip" data-bs-placement="top" title="Editar Material Padrão">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <a href="{% url 'material-padrao-delete' material_padrao.id %}" class="btn btn-sm btn-danger" data-bs-toggle="tooltip" data-bs-placement="top" title="Excluir Material Padrão">
                                            <i class="fas fa-trash"></i>
                                        </a>
                                    </td>
                                </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>

                {% if is_paginated %}
                <nav aria-label="Paginação">
                    <ul class="pagination justify-content-center">
                        {% if page_obj.has_previous %}
                            <li class="page-item">
                                <a class="page-link" href="?page=1" aria-label="Primeira">
                                    <span aria-hidden="true">&laquo;&laquo;</span>
                                </a>
                            </li>
                            <li class="page-item">
                                <a class="page-link" href="?page={{ page_obj.previous_page_number }}" aria-label="Anterior">
                                    <span aria-hidden="true">&laquo;</span>
                                </a>
                            </li>
                        {% else %}
                            <li class="page-item disabled">
                                <a class="page-link" href="#" aria-label="Primeira">
                                    <span aria-hidden="true">&laquo;&laquo;</span>
                                </a>
                            </li>
                            <li class="page-item disabled">
                                <a class="page-link" href="#" aria-label="Anterior">
                                    <span aria-hidden="true">&laquo;</span>
                                </a>
                            </li>
                        {% endif %}

                        {% for num in page_obj.paginator.page_range %}
                            {% if page_obj.number == num %}
                                <li class="page-item active">
                                    <a class="page-link" href="?page={{ num }}">{{ num }}</a>
                                </li>
                            {% elif num > page_obj.number|add:'-3' and num < page_obj.number|add:'3' %}
                                <li class="page-item">
                                    <a class="page-link" href="?page={{ num }}">{{ num }}</a>
                                </li>
                            {% endif %}
                        {% endfor %}

                        {% if page_obj.has_next %}
                            <li class="page-item">
                                <a class="page-link" href="?page={{ page_obj.next_page_number }}" aria-label="Próxima">
                                    <span aria-hidden="true">&raquo;</span>
                                </a>
                            </li>
                            <li class="page-item">
                                <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}" aria-label="Última">
                                    <span aria-hidden="true">&raquo;&raquo;</span>
                                </a>
                            </li>
                        {% else %}
                            <li class="page-item disabled">
                                <a class="page-link" href="#" aria-label="Próxima">
                                    <span aria-hidden="true">&raquo;</span>
                                </a>
                            </li>
                            <li class="page-item disabled">
                                <a class="page-link" href="#" aria-label="Última">
                                    <span aria-hidden="true">&raquo;&raquo;</span>
                                </a>
                            </li>
                        {% endif %}
                    </ul>
                </nav>
                {% endif %}
            {% else %}
                <div class="alert alert-info">
                    <i class="fas fa-info-circle"></i> Nenhum material padrão cadastrado.
                </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}
