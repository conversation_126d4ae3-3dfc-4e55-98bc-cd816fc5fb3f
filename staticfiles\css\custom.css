/* Tema escuro personalizado para Molas <PERSON>s */

:root {
    --dark-bg: #121212;
    --dark-surface: #1e1e1e;
    --dark-primary: #bb86fc;
    --dark-secondary: #03dac6;
    --dark-error: #cf6679;
    --dark-text-primary: rgba(255, 255, 255, 0.87);
    --dark-text-secondary: rgba(255, 255, 255, 0.6);
}

/* Estilos gerais */
body {
    background-color: var(--dark-bg);
    color: var(--dark-text-primary);
    font-family: 'Roboto', sans-serif;
}

/* Navbar */
.navbar {
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
}

/* Sidebar */
.sidebar {
    min-height: calc(100vh - 56px);
    background-color: var(--dark-surface);
    padding-top: 20px;
    border-right: 1px solid rgba(255, 255, 255, 0.1);
    box-shadow: 2px 0 5px rgba(0, 0, 0, 0.2);
}

.sidebar .nav-link {
    color: var(--dark-text-secondary);
    padding: 10px 20px;
    margin-bottom: 5px;
    border-radius: 4px;
    transition: all 0.3s ease;
}

.sidebar .nav-link:hover {
    background-color: rgba(255, 255, 255, 0.05);
    color: var(--dark-text-primary);
}

.sidebar .nav-link.active {
    background-color: var(--dark-primary);
    color: #000;
    font-weight: 500;
}

.sidebar .nav-link i {
    margin-right: 10px;
    width: 20px;
    text-align: center;
}

/* Cards */
.card {
    background-color: var(--dark-surface);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 8px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    margin-bottom: 20px;
}

.card-header {
    background-color: rgba(255, 255, 255, 0.05);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    padding: 15px 20px;
    font-weight: 500;
}

/* Tabelas */
.table {
    color: var(--dark-text-primary);
}

.table-striped > tbody > tr:nth-of-type(odd) {
    background-color: rgba(255, 255, 255, 0.03);
}

.table-bordered {
    border-color: rgba(255, 255, 255, 0.1);
}

.table th {
    background-color: rgba(255, 255, 255, 0.05);
    color: var(--dark-primary);
    font-weight: 500;
    border-bottom: 2px solid rgba(255, 255, 255, 0.1);
}

/* Formulários */
.form-control, .form-select {
    background-color: var(--dark-surface);
    border-color: rgba(255, 255, 255, 0.2);
    color: var(--dark-text-primary);
    border-radius: 6px;
    padding: 10px 15px;
    transition: all 0.3s ease;
}

.form-control:focus, .form-select:focus {
    background-color: var(--dark-surface);
    color: var(--dark-text-primary);
    border-color: var(--dark-primary);
    box-shadow: 0 0 0 0.25rem rgba(187, 134, 252, 0.25);
}

/* Corrigir opções dentro dos selects para exibir apenas uma linha */
select.form-control option,
select.form-select option {
    padding: 8px 12px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    background-color: var(--dark-surface);
    color: var(--dark-text-primary);
    border: none;
    line-height: 1.2;
}

.form-label {
    color: var(--dark-primary);
    font-weight: 500;
    margin-bottom: 8px;
}

/* Botões */
.btn {
    border-radius: 6px;
    padding: 8px 16px;
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.btn-primary {
    background-color: var(--dark-primary);
    border-color: var(--dark-primary);
    color: #000;
}

.btn-primary:hover {
    background-color: #a370db;
    border-color: #a370db;
    color: #000;
}

.btn-success {
    background-color: var(--dark-secondary);
    border-color: var(--dark-secondary);
    color: #000;
}

.btn-success:hover {
    background-color: #02b8a7;
    border-color: #02b8a7;
    color: #000;
}

/* Badges */
.badge {
    padding: 6px 10px;
    font-weight: 500;
    border-radius: 4px;
}

/* Alertas */
.alert {
    border-radius: 8px;
    border: none;
    padding: 15px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    position: relative;
}

.alert-dismissible {
    padding-right: 40px;
}

/* Ocultar completamente os alertas de sucesso (pop-ups verdes) */
.alert-success {
    display: none !important;
}

.alert .btn-close {
    position: absolute !important;
    top: 15px !important;
    right: 15px !important;
    z-index: 2 !important;
    padding: 0.5rem !important;
    color: #fff !important;
    background: transparent !important;
    background-image: none !important;
    border: 0 !important;
    opacity: 0.7 !important;
    font-size: 1.2rem !important;
    width: 24px !important;
    height: 24px !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    box-shadow: none !important;
}

.alert .btn-close::before {
    content: "×" !important;
    font-weight: bold !important;
    font-size: 1.5rem !important;
    line-height: 1 !important;
}

/* Cores específicas para botões de fechamento em diferentes tipos de alertas */
.alert-success .btn-close {
    color: #fff !important;
    text-shadow: 0 1px 0 rgba(0, 0, 0, 0.5) !important;
}

.alert-danger .btn-close {
    color: #fff !important;
    text-shadow: 0 1px 0 rgba(0, 0, 0, 0.5) !important;
}

.alert-warning .btn-close {
    color: #000 !important;
    text-shadow: 0 1px 0 rgba(255, 255, 255, 0.5) !important;
}

.alert-info .btn-close {
    color: #fff !important;
    text-shadow: 0 1px 0 rgba(0, 0, 0, 0.5) !important;
}

.alert .btn-close:hover {
    opacity: 1 !important;
    background-color: rgba(255, 255, 255, 0.1) !important;
    border-radius: 50% !important;
}

/* Sobrescrever estilos padrão do Bootstrap para o botão de fechamento */
button.btn-close {
    box-sizing: content-box !important;
    background: none !important;
    border: 0 !important;
    border-radius: 0.25rem !important;
    opacity: 0.7 !important;
    background-image: none !important;
}

/* Sobrescrever o pseudo-elemento ::after do Bootstrap */
button.btn-close::after {
    display: none !important;
}

.alert-container {
    position: fixed;
    top: 70px;
    right: 20px;
    z-index: 1050;
    max-width: 350px;
}

/* Paginação */
.pagination .page-link {
    background-color: var(--dark-surface);
    border-color: rgba(255, 255, 255, 0.1);
    color: var(--dark-text-secondary);
}

.pagination .page-link:hover {
    background-color: rgba(255, 255, 255, 0.05);
    color: var(--dark-text-primary);
}

.pagination .page-item.active .page-link {
    background-color: var(--dark-primary);
    border-color: var(--dark-primary);
    color: #000;
}

/* Dashboard */
.text-primary {
    color: var(--dark-primary) !important;
}

.text-success {
    color: var(--dark-secondary) !important;
}

.text-gray-800 {
    color: var(--dark-text-primary) !important;
}

.text-gray-300 {
    color: var(--dark-text-secondary) !important;
}

.border-left-primary {
    border-left: 4px solid var(--dark-primary);
}

.border-left-success {
    border-left: 4px solid var(--dark-secondary);
}

.border-left-warning {
    border-left: 4px solid #ffc107;
}

.border-left-danger {
    border-left: 4px solid #dc3545;
}

/* Footer */
.footer {
    background-color: var(--dark-surface);
    padding: 15px 0;
    text-align: center;
    margin-top: 30px;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

/* Animações */
@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

.alert {
    animation: fadeIn 0.5s ease-in-out;
}

/* Tooltips */
.tooltip {
    opacity: 0.8;
    font-size: 0.8rem;
}

.tooltip .tooltip-inner {
    background-color: var(--dark-surface);
    border: 1px solid rgba(255, 255, 255, 0.1);
    color: var(--dark-text-secondary);
    padding: 4px 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.tooltip.bs-tooltip-top .tooltip-arrow::before {
    border-top-color: var(--dark-surface);
}

.tooltip.bs-tooltip-bottom .tooltip-arrow::before {
    border-bottom-color: var(--dark-surface);
}

.tooltip.bs-tooltip-start .tooltip-arrow::before {
    border-left-color: var(--dark-surface);
}

.tooltip.bs-tooltip-end .tooltip-arrow::before {
    border-right-color: var(--dark-surface);
}

/* Responsividade */
@media (max-width: 768px) {
    .sidebar {
        position: fixed;
        top: 56px;
        left: 0;
        width: 100%;
        z-index: 1030;
        height: auto;
        min-height: auto;
        padding-top: 10px;
        padding-bottom: 10px;
    }

    .content {
        margin-top: 60px;
    }
}
